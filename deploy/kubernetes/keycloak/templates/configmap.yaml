{{- if .Values.customConfig }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "tourdecloud-keycloak.fullname" . }}-config
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tourdecloud-keycloak.labels" . | nindent 4 }}
data:
  {{- if .Values.customConfig.realms }}
  realms.json: |
    {{- .Values.customConfig.realms | toJson | nindent 4 }}
  {{- end }}
  
  {{- if .Values.customConfig.themes }}
  themes.json: |
    {{- .Values.customConfig.themes | toJson | nindent 4 }}
  {{- end }}
  
  {{- if .Values.customConfig.providers }}
  providers.json: |
    {{- .Values.customConfig.providers | toJson | nindent 4 }}
  {{- end }}

  {{- if .Values.githubOAuth.enabled }}
  github-oauth-info.txt: |
    GitHub OAuth Configuration:
    - Client ID will be loaded from environment variable: GITHUB_CLIENT_ID
    - Client Secret will be loaded from environment variable: GITHUB_CLIENT_SECRET
    - Redirect URI should be: {{ .Values.keycloak.ingress.hostname }}/realms/{realm-name}/broker/github/endpoint

    To complete the setup:
    1. Create a GitHub OAuth App at https://github.com/settings/applications/new
    2. Set the Authorization callback URL to the redirect URI above
    3. Configure the client ID and secret in the appropriate secret
  {{- end }}
{{- end }}
