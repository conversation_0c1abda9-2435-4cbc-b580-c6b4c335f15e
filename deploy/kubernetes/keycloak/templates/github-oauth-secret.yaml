{{- if and .Values.githubOAuth.enabled (not .Values.githubOAuth.existingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "tourdecloud-keycloak.fullname" . }}-github-oauth
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tourdecloud-keycloak.labels" . | nindent 4 }}
type: Opaque
data:
  {{- if .Values.githubOAuth.clientId }}
  client-id: {{ .Values.githubOAuth.clientId | b64enc | quote }}
  {{- end }}
  {{- if .Values.githubOAuth.clientSecret }}
  client-secret: {{ .Values.githubOAuth.clientSecret | b64enc | quote }}
  {{- end }}
{{- end }}
