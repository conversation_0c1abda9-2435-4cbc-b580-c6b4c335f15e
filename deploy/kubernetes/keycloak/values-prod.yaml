# Production environment values for Keycloak
# This file contains production-specific overrides

# Global configuration for production
global:
  namespace: "keycloak"

keycloak:
  # Production admin credentials (should use existing secrets)
  auth:
    adminUser: admin
    existingSecret: "keycloak-admin-secret"
    passwordSecretKey: "admin-password"
  
  # Production ingress configuration
  ingress:
    enabled: true
    hostname: auth.tourdecloud.com
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
    tls: true
    extraTls:
      - hosts:
          - auth.tourdecloud.com
        secretName: keycloak-tls-secret
  
  # PostgreSQL configuration for production
  postgresql:
    enabled: true
    auth:
      existingSecret: "keycloak-postgresql-secret"
      secretKeys:
        adminPasswordKey: "postgres-password"
        userPasswordKey: "user-password"
    architecture: standalone
    primary:
      persistence:
        enabled: true
        size: 50Gi
        storageClass: "standard"
      resources:
        limits:
          cpu: 1000m
          memory: 1Gi
        requests:
          cpu: 500m
          memory: 512Mi
  
  # Production persistence configuration
  persistence:
    enabled: true
    size: 20Gi
    storageClass: "standard"

  # Production-specific environment variables
  extraEnvVars:
    - name: KEYCLOAK_LOG_LEVEL
      value: "INFO"
    - name: KC_LOG_LEVEL
      value: "INFO"
    - name: KC_HOSTNAME_STRICT
      value: "true"
    - name: KC_HOSTNAME_STRICT_HTTPS
      value: "true"
    - name: KC_HTTP_ENABLED
      value: "false"
    - name: KC_PROXY
      value: "edge"
    - name: KC_HEALTH_ENABLED
      value: "true"
    - name: KC_METRICS_ENABLED
      value: "true"
    - name: GITHUB_CLIENT_ID
      valueFrom:
        secretKeyRef:
          name: keycloak-github-oauth-secret
          key: client-id
    - name: GITHUB_CLIENT_SECRET
      valueFrom:
        secretKeyRef:
          name: keycloak-github-oauth-secret
          key: client-secret

  # Production network policy configuration (disable Bitnami's to avoid conflicts)
  networkPolicy:
    enabled: false
    allowExternal: true
  
  # Production probes with longer timeouts
  livenessProbe:
    initialDelaySeconds: 300
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 5
  
  readinessProbe:
    initialDelaySeconds: 60
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  # Security context for production
  podSecurityContext:
    enabled: true
    fsGroup: 1001
    runAsNonRoot: true
    seccompProfile:
      type: RuntimeDefault
  
  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsNonRoot: true
    readOnlyRootFilesystem: false
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
  
  # Node affinity for production workloads
  nodeAffinityPreset:
    type: "hard"
    key: "node-type"
    values:
      - "production"
  
  # Pod anti-affinity for high availability
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchLabels:
                app.kubernetes.io/name: keycloak
                app.kubernetes.io/instance: tourdecloud-keycloak
            topologyKey: kubernetes.io/hostname

# Network policy configuration for production
networkPolicy:
  enabled: true  # Enabled for production security
  allowExternal: true
  additionalRules:
    ingress: []
    egress: []

# GitHub OAuth configuration for production
githubOAuth:
  enabled: true
  existingSecret: "keycloak-github-oauth-secret"
  
  # Tolerations for dedicated nodes
  tolerations:
    - key: "dedicated"
      operator: "Equal"
      value: "keycloak"
      effect: "NoSchedule"
  
  # Production annotations
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/metrics"

# Production custom configuration
customConfig:
  realms:
    tourdecloud:
      realm: "tourdecloud"
      displayName: "Tour de Cloud"
      enabled: true
      sslRequired: "external"
      registrationAllowed: false
      loginWithEmailAllowed: true
      duplicateEmailsAllowed: false
      resetPasswordAllowed: true
      editUsernameAllowed: false
      bruteForceProtected: true
      permanentLockout: false
      maxFailureWaitSeconds: 900
      minimumQuickLoginWaitSeconds: 60
      waitIncrementSeconds: 60
      quickLoginCheckMilliSeconds: 1000
      maxDeltaTimeSeconds: 43200
      failureFactor: 30
      identityProviders:
        - alias: "github"
          providerId: "github"
          enabled: true
          updateProfileFirstLoginMode: "on"
          trustEmail: false
          storeToken: false
          addReadTokenRoleOnCreate: false
          authenticateByDefault: false
          linkOnly: false
          firstBrokerLoginFlowAlias: "first broker login"
          config:
            clientId: "${GITHUB_CLIENT_ID}"
            clientSecret: "${GITHUB_CLIENT_SECRET}"
            defaultScope: "user:email"
            syncMode: "IMPORT"
            useJwksUrl: "true"
          mappers:
            - name: "github-username"
              identityProviderMapper: "github-user-attribute-mapper"
              config:
                syncMode: "INHERIT"
                attribute: "login"
                user.attribute: "username"
            - name: "github-email"
              identityProviderMapper: "github-user-attribute-mapper"
              config:
                syncMode: "INHERIT"
                attribute: "email"
                user.attribute: "email"
            - name: "github-first-name"
              identityProviderMapper: "github-user-attribute-mapper"
              config:
                syncMode: "INHERIT"
                attribute: "name"
                user.attribute: "firstName"
      clients:
        - clientId: "tourdecloud-app"
          name: "Tour de Cloud Application"
          description: "Main application client for Tour de Cloud"
          enabled: true
          clientAuthenticatorType: "client-secret"
          secret: "${KEYCLOAK_CLIENT_SECRET}"
          redirectUris:
            - "https://app.tourdecloud.com/*"
            - "https://auth.tourdecloud.com/*"
          webOrigins:
            - "https://app.tourdecloud.com"
            - "https://auth.tourdecloud.com"
          standardFlowEnabled: true
          implicitFlowEnabled: false
          directAccessGrantsEnabled: false
          serviceAccountsEnabled: true
          publicClient: false
          frontchannelLogout: true
          protocol: "openid-connect"
          attributes:
            "access.token.lifespan": "300"
            "sso.session.idle.timeout": "1800"
            "sso.session.max.lifespan": "36000"
            "client.session.idle.timeout": "1800"
            "client.session.max.lifespan": "36000"
      roles:
        realm:
          - name: "user"
            description: "Standard user role"
          - name: "admin"
            description: "Administrator role"
          - name: "service-account"
            description: "Service account role"
        client:
          tourdecloud-app:
            - name: "app-user"
              description: "Application user"
            - name: "app-admin"
              description: "Application administrator"
            - name: "app-service"
              description: "Application service account"