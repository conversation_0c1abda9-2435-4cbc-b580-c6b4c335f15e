# Development environment values for Keycloak
# This file contains development-specific overrides

# Global configuration for development
global:
  namespace: "keycloak"

keycloak:
  # Development admin credentials (use stronger passwords in production)
  auth:
    adminUser: admin
    adminPassword: "dev-admin-password"
  
  # Development ingress configuration (disabled for port-forward access)
  ingress:
    enabled: false
    hostname: keycloak-dev.local
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/ssl-redirect: "false"
    tls: false

  # Development-specific environment variables
  extraEnvVars:
    - name: KEYCLOAK_LOG_LEVEL
      value: "DEBUG"
    - name: KC_LOG_LEVEL
      value: "DEBUG"
    - name: KC_HOSTNAME_STRICT
      value: "false"
    - name: KC_HOSTNAME_STRICT_HTTPS
      value: "false"
    - name: KC_HTTP_ENABLED
      value: "true"
    - name: KC_HOSTNAME_URL
      value: "http://localhost:8080"
    - name: KC_HOSTNAME_ADMIN_URL
      value: "http://localhost:8080"
    - name: GITHUB_CLIENT_ID
      valueFrom:
        secretKeyRef:
          name: tourdecloud-keycloak-github-oauth
          key: client-id
          optional: true
    - name: GITHUB_CLIENT_SECRET
      valueFrom:
        secretKeyRef:
          name: tourdecloud-keycloak-github-oauth
          key: client-secret
          optional: true
  
  # Faster startup for development
  livenessProbe:
    initialDelaySeconds: 120
    periodSeconds: 10
  
  readinessProbe:
    initialDelaySeconds: 30
    periodSeconds: 5
  networkPolicy:
    enabled: false  # Disabled for easier development access
    allowExternal: true

# Network policy configuration for development (our custom template)
networkPolicy:
  enabled: false  # Disabled for easier development access
  allowExternal: true

# Keycloak-specific network policy configuration for development

# GitHub OAuth configuration for development
githubOAuth:
  enabled: true
  clientId: ""  # Set this to your GitHub OAuth App Client ID
  clientSecret: ""  # Set this to your GitHub OAuth App Client Secret

# Development custom configuration
customConfig:
  realms:
    tourdecloud-dev:
      realm: "tourdecloud-dev"
      displayName: "Tour de Cloud Development"
      enabled: true
      sslRequired: "external"
      registrationAllowed: true
      loginWithEmailAllowed: true
      duplicateEmailsAllowed: false
      resetPasswordAllowed: true
      editUsernameAllowed: false
      bruteForceProtected: true
      identityProviders:
        - alias: "github"
          providerId: "github"
          enabled: true
          updateProfileFirstLoginMode: "on"
          trustEmail: false
          storeToken: false
          addReadTokenRoleOnCreate: false
          authenticateByDefault: false
          linkOnly: false
          firstBrokerLoginFlowAlias: "first broker login"
          config:
            clientId: "${GITHUB_CLIENT_ID}"
            clientSecret: "${GITHUB_CLIENT_SECRET}"
            defaultScope: "user:email"
            syncMode: "IMPORT"
            useJwksUrl: "true"
          mappers:
            - name: "github-username"
              identityProviderMapper: "github-user-attribute-mapper"
              config:
                syncMode: "INHERIT"
                attribute: "login"
                user.attribute: "username"
            - name: "github-email"
              identityProviderMapper: "github-user-attribute-mapper"
              config:
                syncMode: "INHERIT"
                attribute: "email"
                user.attribute: "email"
            - name: "github-first-name"
              identityProviderMapper: "github-user-attribute-mapper"
              config:
                syncMode: "INHERIT"
                attribute: "name"
                user.attribute: "firstName"
      clients:
        - clientId: "tourdecloud-app"
          name: "Tour de Cloud Application"
          description: "Main application client for Tour de Cloud"
          enabled: true
          clientAuthenticatorType: "client-secret"
          redirectUris:
            - "http://localhost:3000/*"
            - "http://localhost:8080/*"
            - "http://keycloak-dev.local/*"
          webOrigins:
            - "http://localhost:3000"
            - "http://localhost:8080"
            - "http://keycloak-dev.local"
          standardFlowEnabled: true
          implicitFlowEnabled: false
          directAccessGrantsEnabled: true
          serviceAccountsEnabled: false
          publicClient: true
          frontchannelLogout: true
          protocol: "openid-connect"
          attributes:
            "saml.assertion.signature": "false"
            "saml.force.post.binding": "false"
            "saml.multivalued.roles": "false"
            "saml.encrypt": "false"
            "saml.server.signature": "false"
            "saml.server.signature.keyinfo.ext": "false"
            "exclude.session.state.from.auth.response": "false"
            "saml_force_name_id_format": "false"
            "saml.client.signature": "false"
            "tls.client.certificate.bound.access.tokens": "false"
            "saml.authnstatement": "false"
            "display.on.consent.screen": "false"
            "saml.onetimeuse.condition": "false"
      users:
        - username: "testuser"
          email: "<EMAIL>"
          firstName: "Test"
          lastName: "User"
          enabled: true
          emailVerified: true
          credentials:
            - type: "password"
              value: "testpassword"
              temporary: false
          realmRoles:
            - "user"
          clientRoles:
            tourdecloud-app:
              - "app-user"
      roles:
        realm:
          - name: "user"
            description: "Standard user role"
          - name: "admin"
            description: "Administrator role"
        client:
          tourdecloud-app:
            - name: "app-user"
              description: "Application user"
            - name: "app-admin"
              description: "Application administrator"