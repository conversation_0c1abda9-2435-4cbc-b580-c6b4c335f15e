# Default values for Tour de Cloud Keycloak deployment
# This is a YAML-formatted file.

# Global configuration
global:
  storageClass: ""
  # Namespace configuration
  namespace: "keycloak"

# Keycloak configuration
keycloak:
  enabled: true
  
  # Keycloak admin credentials
  auth:
    adminUser: admin
    # adminPassword: "" # Will be auto-generated if not set
    existingSecret: ""
    passwordSecretKey: ""
  
  # Keycloak configuration
  configuration: ""
  existingConfigmap: ""
  
  # Extra environment variables
  extraEnvVars: []

  # Extra environment variables from secrets
  extraEnvVarsSecret: ""
  
  # Keycloak service configuration
  service:
    type: ClusterIP
    ports:
      http: 80
      https: 443
    nodePorts:
      http: ""
      https: ""
    sessionAffinity: None
    sessionAffinityConfig: {}
    clusterIP: ""
    loadBalancerIP: ""
    loadBalancerSourceRanges: []
    externalTrafficPolicy: Cluster
    annotations: {}
    extraPorts: []
  
  # Ingress configuration
  ingress:
    enabled: false
    hostname: keycloak.local
    annotations: {}
    tls: false
    selfSigned: false
    extraHosts: []
    extraPaths: []
    extraTls: []
    secrets: []
    ingressClassName: ""
  
  # Production resources
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 1Gi

  # Multiple replicas for high availability
  replicaCount: 2

  # Enable pod disruption budget
  pdb:
    create: true
    minAvailable: 1

  # Enable autoscaling
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 2
    targetCPU: 70
    targetMemory: 80

  # Persistence
  persistence:
    enabled: true
    size: 20Gi
    storageClass: "fast-ssd"
  
  # PostgreSQL database configuration
  postgresql:
    enabled: true
    auth:
      postgresPassword: ""
      username: keycloak
      password: ""
      database: keycloak
      existingSecret: ""
      secretKeys:
        adminPasswordKey: postgres-password
        userPasswordKey: password
    architecture: standalone
    primary:
      persistence:
        enabled: true
        size: 8Gi
        storageClass: ""
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 250m
          memory: 256Mi

  # Keycloak startup and liveness probes
  livenessProbe:
    enabled: true
    initialDelaySeconds: 300
    periodSeconds: 1
    timeoutSeconds: 5
    failureThreshold: 3
    successThreshold: 1
  
  readinessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 3
    successThreshold: 1
  
  # Security context
  podSecurityContext:
    enabled: true
    fsGroup: 1001
  
  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsNonRoot: true
    readOnlyRootFilesystem: false
  
  # Node affinity and tolerations
  nodeAffinityPreset:
    type: ""
    key: ""
    values: []
  
  affinity: {}
  nodeSelector: {}
  tolerations: []
  
  # Pod annotations and labels
  podAnnotations: {}
  podLabels: {}
  
  # Service account
  serviceAccount:
    create: true
    name: ""
    annotations: {}
    automountServiceAccountToken: true
  
  # RBAC
  rbac:
    create: false
    rules: []
  
  # Network policies
  networkPolicy:
    enabled: true
    allowExternal: true
    additionalRules:
      ingress: []
      egress: []

# GitHub OAuth configuration
# To enable GitHub as an identity provider:
# 1. Create a GitHub OAuth App at https://github.com/settings/applications/new
# 2. Set the Authorization callback URL to: https://your-keycloak-domain/realms/your-realm/broker/github/endpoint
# 3. Set clientId and clientSecret below, or use existingSecret for production
githubOAuth:
  enabled: false
  clientId: ""
  clientSecret: ""
  # Use existing secret instead of creating one (recommended for production)
  existingSecret: ""
  clientIdKey: "client-id"
  clientSecretKey: "client-secret"

# Custom configuration for Tour de Cloud
customConfig:
  # Custom realm configurations
  realms: {}

  # Custom themes
  themes: {}

  # Custom providers
  providers: {}

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: "keycloak"  # ServiceMonitor will be created in the keycloak namespace
    interval: "30s"
    scrapeTimeout: "10s"
    labels:
      app: "keycloak"
      environment: "staging"

# Backup
backup:
  enabled: true
  schedule: "0 3 * * *"
  retention: "14d"
  storageClass: "standard"
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
